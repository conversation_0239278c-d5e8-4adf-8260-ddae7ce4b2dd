export const ERROR_CODES = {
  CHAIN: {
    UNSUPPORTED_CHAIN: "UNSUPPORTED_CHAIN",
    UNSUPPORTED_GAME: "UNSUPPORTED_GAME",
    CHAIN_ID_MISMATCH: "CHAIN_ID_MISMATCH",
  },
  TRANSACTION: {
    TOKEN_APPROVAL_ERROR: "TOKEN_APPROVAL_ERROR",
    TOKEN_METADATA_ERROR: "TOKEN_METADATA_ERROR",
  },
  READ: {
    CHAINLINK_VRF_COST_ERROR: "CHAINLINK_VRF_COST_ERROR",
  },
  GAME: {
    PLACE_BET_ERROR: "PLACE_BET_ERROR",
    PLACE_FREEBET_ERROR: "PLACE_FREEBET_ERROR",
    PLACE_BET_EVENT_NOT_FOUND: "PLACE_BET_EVENT_NOT_FOUND",
    ROLL_EVENT_ERROR: "ROLL_EVENT_ERROR",
    R<PERSON><PERSON>_EVENT_TIMEOUT: "ROLL_EVENT_TIMEOUT",
    GET_PAUSED_ERROR: "GET_PAUSED_ERROR",
    GET_TOKEN_ERROR: "GET_TOKEN_ERROR",
    GET_AFFILIATE_HOUSE_EDGE_ERROR: "GET_AFFILIATE_HOUSE_EDGE_ERROR",
    GET_KENO_CONFIGURATION_ERROR: "GET_KENO_CONFIGURATION_ERROR",
    GET_WEIGHTED_GAME_CONFIGURATION_ERROR: "GET_WEIGHTED_GAME_CONFIGURATION_ERROR",
  },
  BANK: {
    GET_TOKENS_ERROR: "GET_TOKENS_ERROR",
    GET_BET_REQUIREMENTS_ERROR: "GET_BET_REQUIREMENTS_ERROR",
  },
  SUBGRAPH: {
    FETCH_BET_ERROR: "FETCH_BET_ERROR",
    FETCH_BETS_ERROR: "FETCH_BETS_ERROR",
    FETCH_TOKENS_ERROR: "FETCH_TOKENS_ERROR",
    FETCH_TOKEN_ERROR: "FETCH_TOKEN_ERROR",
  },
  WALLET: {
    ACCOUNT_MISSING: "ACCOUNT_MISSING",
    GET_TRANSACTION_RECEIPT_ERROR: "GET_TRANSACTION_RECEIPT_ERROR",
  },
  LEADERBOARD: {
    TOO_MANY_BETS: "TOO_MANY_BETS",
    GET_CLAIMABLE_AMOUNT_ERROR: "GET_CLAIMABLE_AMOUNT_ERROR",
    UNSUPPORTED_CHAIN: "UNSUPPORTED_CHAIN",
    NO_CLAIMABLE_AMOUNT: "NO_CLAIMABLE_AMOUNT",
  },
} as const;

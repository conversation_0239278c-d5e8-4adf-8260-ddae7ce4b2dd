# Testing the @betswirl/ui Library

This guide explains how to test the library package in a separate React project.

## Quick Test Setup

### 1. Create a new React project

```bash
npx create-react-app test-casino-app --template typescript
cd test-casino-app
```

### 2. Install required dependencies

```bash
npm install @coinbase/onchainkit @tanstack/react-query viem wagmi tailwindcss
```

### 3. Install the library package

```bash
# Install from the packed tarball
npm install ../path/to/betswirl-ui-0.0.15.tgz
```

### 4. Set up Tailwind CSS

```bash
npx tailwindcss init -p
```

Update `tailwind.config.js`:

```javascript
/** @type {import('tailwindcss').Config} */
module.exports = {
  content: [
    "./src/**/*.{js,ts,jsx,tsx}",
    // 👇 ВАЖНО: Добавьте путь к компонентам библиотеки для сканирования утилит
    "./node_modules/@betswirl/ui/dist/**/*.{js,ts,jsx,tsx}",
  ],
  theme: {
    extend: {},
  },
  plugins: [
    // 👇 ВАЖНО: Подключите плагин @betswirl/ui
    require("@betswirl/ui/plugin"),
  ],
}
```

Add to `src/index.css`:

```css
@tailwind base;
@tailwind components;
@tailwind utilities;
```

### 5. Set up environment variables

Create `.env.local`:

```bash
REACT_APP_RPC_URL=your_rpc_url_here
REACT_APP_AFFILIATE_ADDRESS=your_affiliate_address_here
```

### 6. Update App.tsx

Replace the contents of `src/App.tsx`:

```typescript
import React from "react"
// 👇 БОЛЬШЕ НЕ НУЖНО импортировать CSS - стили теперь генерируются через плагин Tailwind
import { AppProviders, RouletteGame } from "@betswirl/ui"

function App() {
  return (
    <AppProviders>
      <div className="min-h-screen bg-gray-100 p-4">
        <h1 className="text-2xl font-bold mb-4">Casino Games Test</h1>
        <RouletteGame />
      </div>
    </AppProviders>
  )
}

export default App
```

### 7. Run the test app

```bash
npm start
```

## Testing Different Components

### Test Coin Toss Game

```typescript
import { CoinTossGame } from "@betswirl/ui"

// Replace RouletteGame with CoinTossGame in App.tsx
;<CoinTossGame />
```

### Test Dice Game

```typescript
import { DiceGame } from "@betswirl/ui"

// Replace RouletteGame with DiceGame in App.tsx
;<DiceGame />
```

### Test Custom Themes

```typescript
<RouletteGame
  theme="dark"
  customTheme={{
    "--primary": "#ff6b35",
    "--play-btn-font": "bold",
  }}
/>
```

## Common Issues and Solutions

### 1. Стили не применяются

- Убедитесь, что плагин `@betswirl/ui/plugin` подключен в `tailwind.config.js`
- Проверьте, что путь к компонентам библиотеки добавлен в `content` секцию
- Убедитесь, что Tailwind CSS правильно настроен и собирается

### 2. Компоненты не отображаются

- Убедитесь, что все peer dependencies установлены
- Проверьте, что используется обертка AppProviders
- Проверьте переменные окружения

### 3. Ошибки TypeScript

- Убедитесь, что TypeScript декларации найдены
- Проверьте, что библиотека собрана с TypeScript декларациями

### 4. Ошибки сборки

- Убедитесь, что все peer dependencies соответствуют требуемым версиям
- Проверьте конфликты зависимостей
- Убедитесь, что плагин Tailwind правильно подключен

## Expected Behavior

When everything is set up correctly, you should see:

1. A fully functional game interface
2. Wallet connection button
3. Game controls (betting interface)
4. Info and history panels
5. Proper styling and theming

The games should be interactive and ready for Web3 integration once proper RPC and affiliate settings are configured.

import { BP_VALUE } from "@betswirl/sdk-core"
import { useCallback, useEffect, useRef, useState } from "react"
import { WheelSegment } from "../components/game/WheelGameControls"

interface UseWheelAnimationParams {
  spinDuration: number
  continuousSpinDuration: number
  segments: WheelSegment[]
  onSpinComplete?: () => void
}

interface UseWheelAnimationReturn {
  rotationAngle: number
  displayedMultiplier: number
  hasResult: boolean
  isSpinning: boolean
  isTransitionEnabled: boolean
  winningSectorIndex: number | null
  startEndlessSpin: () => void
  spinWheelWithResult: (sectorIndex: number) => void
  stopSpin: () => void
}

const ANGLE_VARIANCE = 32
const MIN_FULL_ROTATIONS = 5
const MAX_FULL_ROTATIONS = 8
const CONTINUOUS_SPIN_SPEED = 4

function getTargetAngleForSectorIndex(
  segments: WheelSegment[],
  winningSectorIndex: number,
): number {
  const segment = segments[winningSectorIndex]
  if (!segment) {
    console.warn(`Could not find segment for sector index: ${winningSectorIndex}`)
    return 0
  }
  const randomOffset = (Math.random() - 0.5) * ANGLE_VARIANCE
  const targetAngle = 360 - segment.startAngle + randomOffset
  const fullRotations =
    Math.floor(Math.random() * (MAX_FULL_ROTATIONS - MIN_FULL_ROTATIONS + 1)) + MIN_FULL_ROTATIONS
  return targetAngle + fullRotations * 360
}

export function useWheelAnimation({
  spinDuration,
  segments,
  onSpinComplete,
}: UseWheelAnimationParams): UseWheelAnimationReturn {
  const [rotationAngle, setRotationAngle] = useState(0)
  const [displayedMultiplier, setDisplayedMultiplier] = useState<number>(0)
  const [hasResult, setHasResult] = useState(false)
  const [isSpinning, setIsSpinning] = useState(false)
  const [winningSectorIndex, setWinningSectorIndex] = useState<number | null>(null)
  const [isTransitionEnabled, setIsTransitionEnabled] = useState(false)

  const continuousSpinIntervalRef = useRef<ReturnType<typeof setInterval> | null>(null)
  const spinCompleteTimeoutRef = useRef<ReturnType<typeof setTimeout> | null>(null)

  const cleanupTimers = useCallback(() => {
    if (continuousSpinIntervalRef.current) {
      clearInterval(continuousSpinIntervalRef.current)
      continuousSpinIntervalRef.current = null
    }
    if (spinCompleteTimeoutRef.current) {
      clearTimeout(spinCompleteTimeoutRef.current)
      spinCompleteTimeoutRef.current = null
    }
  }, [])

  useEffect(() => {
    if (isSpinning && winningSectorIndex === null) {
      setIsTransitionEnabled(false)

      continuousSpinIntervalRef.current = setInterval(() => {
        setRotationAngle(prevAngle => prevAngle + CONTINUOUS_SPIN_SPEED)
      }, 16)
    }

    if (isSpinning && winningSectorIndex !== null) {
      cleanupTimers()
      setIsTransitionEnabled(true)

      setRotationAngle(prevAngle => {
        const baseTargetAngle = getTargetAngleForSectorIndex(segments, winningSectorIndex)
        let finalAngle = baseTargetAngle
        while (finalAngle < prevAngle) {
          finalAngle += 360
        }
        return finalAngle
      })

      spinCompleteTimeoutRef.current = setTimeout(() => {
        const winningSegment = segments[winningSectorIndex]
        setDisplayedMultiplier(winningSegment.multiplier)
        setHasResult(true)
        setIsSpinning(false)
        onSpinComplete?.()
      }, spinDuration)
    }

    return cleanupTimers
  }, [isSpinning, winningSectorIndex, segments, spinDuration, onSpinComplete, cleanupTimers])

  const startEndlessSpin = useCallback(() => {
    setRotationAngle(prev => prev % 360)
    setHasResult(false)
    setWinningSectorIndex(null)
    setDisplayedMultiplier(0)

    setIsSpinning(true)
  }, [])

  const spinWheelWithResult = useCallback((sectorIndex: number) => {
    if (!isSpinning) {
      startEndlessSpin()
      setTimeout(() => setWinningSectorIndex(sectorIndex), 100)
    } else {
      setWinningSectorIndex(sectorIndex)
    }
  }, [isSpinning, startEndlessSpin])

  const stopSpin = useCallback(() => {
    cleanupTimers()
    setIsSpinning(false)
    setWinningSectorIndex(null)
    setHasResult(false)
    setDisplayedMultiplier(0)
    setRotationAngle(prev => prev % 360)
  }, [cleanupTimers])

  const numericDisplayedMultiplier = displayedMultiplier / BP_VALUE

  return {
    rotationAngle,
    displayedMultiplier: numericDisplayedMultiplier,
    hasResult,
    isSpinning,
    isTransitionEnabled,
    winningSectorIndex,
    startEndlessSpin,
    spinWheelWithResult,
    stopSpin,
  }
}
